import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller/card_controller.dart';
import 'gacha_animation_widget.dart';
import 'card_item_widget.dart';
import '../model/card.dart' as card_model;
import '../model/card_rarity.dart';
import '../../wallet/controller/wallet_controller.dart';
import '../../../common/models/ai_role.dart';

/// Role-specific gacha page
class RoleGachaPage extends GetView<CardController> {
  const RoleGachaPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AiRole role = Get.arguments as AiRole;
    final walletController = Get.find<WalletController>();

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background with main character
          _buildBackgroundImage(role),

          // Top navigation bar
          _buildTopNavigation(walletController),



          // Bottom content area
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottom<PERSON>ontent(role),
          ),
        ],
      ),
    );
  }


  /// Build background image with main character
  Widget _buildBackgroundImage(AiR<PERSON> role) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(role.coverUrl.isNotEmpty ? role.coverUrl : role.avatarUrl),
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  /// Build top navigation bar with character name
  Widget _buildTopNavigation(WalletController walletController) {
    final AiRole role = Get.arguments as AiRole;

    return Positioned(
      top: 50,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back button
            Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white, size: 24),
                onPressed: () => Get.back(),
              ),
            ),

            // Character name with probability button
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  constraints: const BoxConstraints(maxWidth: 150), // 限制最大宽度为150像素
                  child: Text(
                    role.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          offset: Offset(2, 2),
                          blurRadius: 6,
                          color: Colors.black,
                        ),
                        Shadow(
                          offset: Offset(0, 0),
                          blurRadius: 3,
                          color: Colors.black,
                        ),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    _showRateInfo();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.white,
                          size: 14,
                        ),
                        SizedBox(width: 4),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Balance display
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: Colors.yellow.withValues(alpha: 0.9), width: 1.5),
                boxShadow: [
                  BoxShadow(
                    color: Colors.yellow.withValues(alpha: 0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.4),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.diamond, color: Colors.yellow, size: 18),
                  const SizedBox(width: 6),
                  Obx(() => Text(
                    '${walletController.currentBalance}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }







  /// Build bottom content area
  Widget _buildBottomContent(AiRole role) {
    return Container(
      height: 250, // Reduced height for more compact layout
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.3),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 20), // Top spacing

          // Cards horizontal scroll
          _buildCardsHorizontalScroll(role),

          const SizedBox(height: 20), // Spacing between cards and buttons

          // Gacha buttons row (only single and ten pull)
          _buildSimpleGachaButtonsRow(role),

          const SizedBox(height: 20), // Bottom spacing
        ],
      ),
    );
  }



  /// Build cards horizontal scroll
  Widget _buildCardsHorizontalScroll(AiRole role) {
    return SizedBox(
      height: 130, // Reduced height to accommodate smaller cards
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        clipBehavior: Clip.none, // Allow glow effects to show outside bounds
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 6, // Show 6 sample cards
        itemBuilder: (context, index) {
          final card = _createSampleCard(role, index);
          return Container(
            width: 65, // Reduced card width for better spacing
            margin: const EdgeInsets.only(right: 20), // Adjusted margin for smaller cards
            child: CardItemWidget(
              card: card,
              showOwnedCount: false,
              width: 65, // Reduced card width
              height: 95, // Reduced card height
            ),
          );
        },
      ),
    );
  }

  /// Create a sample card from AiRole
  card_model.Card _createSampleCard(AiRole role, [int? index]) {
    // Use different rarities for variety
    final rarities = [
      CardRarity.common,
      CardRarity.rare,
      CardRarity.epic,
      CardRarity.legendary,
      CardRarity.mythic,
    ];

    // Select rarity based on role ID and index for consistency
    final rarityIndex = (role.id + (index ?? 0)) % rarities.length;
    final rarity = rarities[rarityIndex];

    // Simulate owned status - some cards are owned, some are not
    final isOwned = (index ?? 0) % 3 == 0; // Every 3rd card is owned

    return card_model.Card(
      id: 'sample_${role.id}_${index ?? 0}',
      name: '${role.name} ${index != null ? 'Card ${index + 1}' : 'Card'}',
      description: role.description,
      imageUrl: role.coverUrl.isNotEmpty ? role.coverUrl : role.avatarUrl,
      rarity: rarity,
      roleId: role.id.toString(),
      roleName: role.name,
      createdAt: DateTime.now(),
      isOwned: isOwned,
      ownedCount: isOwned ? 1 : 0,
    );
  }



  /// Build simple gacha buttons row (only single and ten pull)
  Widget _buildSimpleGachaButtonsRow(AiRole role) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Single pull button
          Expanded(
            child: _buildGachaButtonNew(
              title: 'Gacha x1',
              cost: 100,
              color: Colors.grey[800]!,
              textColor: Colors.white,
              onTap: () => _performRoleGacha(role, 1),
            ),
          ),

          const SizedBox(width: 16),

          // Ten pull button
          Expanded(
            child: _buildGachaButtonNew(
              title: 'Gacha x10',
              cost: 1000,
              isGoldButton: true,
              textColor: Colors.black,
              onTap: () => _performRoleGacha(role, 10),
            ),
          ),
        ],
      ),
    );
  }

  /// Build new style gacha button
  Widget _buildGachaButtonNew({
    required String title,
    required int cost,
    Color? color,
    bool isGoldButton = false,
    required Color textColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 55, // Reduced height
        decoration: BoxDecoration(
          color: isGoldButton ? null : (color ?? Colors.grey[800]!),
          gradient: isGoldButton ? LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.yellow[600]!,
              Colors.yellow[700]!,
              Colors.orange[600]!,
            ],
          ) : null,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                color: textColor,
                fontSize: 15, // Slightly smaller font
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 1), // Reduced spacing
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Cost: $cost',
                  style: TextStyle(
                    color: textColor.withValues(alpha: 0.8),
                    fontSize: 11, // Smaller font
                  ),
                ),
                const SizedBox(width: 2),
                const Icon(
                  Icons.diamond,
                  color: Colors.blue,
                  size: 11, // Smaller icon
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

/// Show rate information dialog
  void _showRateInfo() {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.5)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Drop Rates',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildRateItem('Mythic', '0.2%', Colors.red),
              _buildRateItem('Legendary', '2.8%', Colors.orange),
              _buildRateItem('Epic', '12%', Colors.purple),
              _buildRateItem('Rare', '25%', Colors.blue),
              _buildRateItem('Common', '60%', Colors.grey),
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.center,
                child: TextButton(
                  onPressed: () => Get.back(),
                  child: const Text(
                    'Close',
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

 /// Build rate item
  Widget _buildRateItem(String rarity, String rate, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            rarity,
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            rate,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Perform role-specific gacha
  void _performRoleGacha(AiRole role, int count) async {
    try {
      // For now, use the general gacha system
      // TODO: Implement role-specific gacha logic
      final result = count == 1
        ? await controller.performSinglePull()
        : await controller.performTenPull();

      // Show animation
      if (result != null) {
        Get.to(() => GachaAnimationWidget(result: result));
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
  
